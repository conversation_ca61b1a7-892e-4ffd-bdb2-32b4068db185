<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情互动页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            cursor: none;
        }

        /* 菜单按钮 */
        .menu-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .menu-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .menu {
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            transform: translateY(-20px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 999;
        }

        .menu.show {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .menu-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            color: #333;
        }

        .menu-item:last-child {
            margin-bottom: 0;
        }

        .toggle-switch {
            position: relative;
            width: 40px;
            height: 20px;
            background: #ccc;
            border-radius: 20px;
            margin-left: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::before {
            transform: translateX(20px);
        }

        /* 笑脸容器 */
        .face-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
        }

        .face {
            width: 100%;
            height: 100%;
            background: #FFD700;
            border-radius: 50%;
            position: relative;
            border: 5px solid #FFA500;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* 眼睛 */
        .eye {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #333;
            border-radius: 50%;
            top: 60px;
            transition: all 0.3s ease;
        }

        .left-eye {
            left: 50px;
        }

        .right-eye {
            right: 50px;
        }

        /* 嘴巴 */
        .mouth {
            position: absolute;
            left: 50%;
            top: 120px;
            transform: translateX(-50%);
            width: 80px;
            height: 40px;
            border: 4px solid #333;
            border-top: none;
            border-radius: 0 0 80px 80px;
            transition: all 0.3s ease;
        }

        /* 按钮样式 */
        .button {
            position: absolute;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .reject-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            top: 20%;
            left: 15%;
        }

        .reject-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .accept-btn {
            background: linear-gradient(45deg, #51cf66, #40c057);
            bottom: 20%;
            right: 15%;
        }

        .accept-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(81, 207, 102, 0.4);
        }

        /* 彩虹拖尾 */
        .trail {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
        }

        /* 自定义鼠标指针 */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.2) 100%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
        }
    </style>
</head>
<body>
    <!-- 菜单 -->
    <button class="menu-toggle" onclick="toggleMenu()">⚙️</button>
    <div class="menu" id="menu">
        <div class="menu-item">
            彩虹拖尾
            <div class="toggle-switch active" id="trailToggle" onclick="toggleTrail()"></div>
        </div>
    </div>

    <!-- 自定义鼠标指针 -->
    <div class="custom-cursor" id="cursor"></div>

    <!-- 笑脸 -->
    <div class="face-container">
        <div class="face" id="face">
            <div class="eye left-eye" id="leftEye"></div>
            <div class="eye right-eye" id="rightEye"></div>
            <div class="mouth" id="mouth"></div>
        </div>
    </div>

    <!-- 按钮 -->
    <button class="button reject-btn" id="rejectBtn">拒绝</button>
    <button class="button accept-btn" id="acceptBtn">同意</button>

    <script>
        let trailEnabled = true;
        let trails = [];
        let mouseX = 0, mouseY = 0;

        // 获取元素
        const face = document.getElementById('face');
        const leftEye = document.getElementById('leftEye');
        const rightEye = document.getElementById('rightEye');
        const mouth = document.getElementById('mouth');
        const rejectBtn = document.getElementById('rejectBtn');
        const acceptBtn = document.getElementById('acceptBtn');
        const cursor = document.getElementById('cursor');

        // 菜单控制
        function toggleMenu() {
            const menu = document.getElementById('menu');
            menu.classList.toggle('show');
        }

        function toggleTrail() {
            const toggle = document.getElementById('trailToggle');
            trailEnabled = !trailEnabled;
            toggle.classList.toggle('active');
            
            if (!trailEnabled) {
                // 清除所有拖尾
                trails.forEach(trail => trail.remove());
                trails = [];
            }
        }

        // 鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            
            // 更新自定义鼠标指针位置
            cursor.style.left = mouseX - 10 + 'px';
            cursor.style.top = mouseY - 10 + 'px';
            
            // 创建彩虹拖尾
            if (trailEnabled) {
                createTrail(mouseX, mouseY);
            }
            
            // 计算表情变化
            updateFaceExpression(mouseX, mouseY);
        });

        // 创建彩虹拖尾
        function createTrail(x, y) {
            const trail = document.createElement('div');
            trail.className = 'trail';
            trail.style.left = x - 3 + 'px';
            trail.style.top = y - 3 + 'px';
            
            // 彩虹颜色
            const colors = ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            trail.style.background = randomColor;
            trail.style.boxShadow = `0 0 10px ${randomColor}`;
            
            document.body.appendChild(trail);
            trails.push(trail);
            
            // 动画效果
            let opacity = 1;
            let scale = 1;
            const fadeOut = setInterval(() => {
                opacity -= 0.05;
                scale += 0.02;
                trail.style.opacity = opacity;
                trail.style.transform = `scale(${scale})`;
                
                if (opacity <= 0) {
                    clearInterval(fadeOut);
                    trail.remove();
                    trails = trails.filter(t => t !== trail);
                }
            }, 50);
        }

        // 更新表情
        function updateFaceExpression(mouseX, mouseY) {
            const rejectRect = rejectBtn.getBoundingClientRect();
            const acceptRect = acceptBtn.getBoundingClientRect();
            
            // 计算到两个按钮的距离
            const distanceToReject = Math.sqrt(
                Math.pow(mouseX - (rejectRect.left + rejectRect.width / 2), 2) +
                Math.pow(mouseY - (rejectRect.top + rejectRect.height / 2), 2)
            );
            
            const distanceToAccept = Math.sqrt(
                Math.pow(mouseX - (acceptRect.left + acceptRect.width / 2), 2) +
                Math.pow(mouseY - (acceptRect.top + acceptRect.height / 2), 2)
            );
            
            // 计算表情系数 (-1 到 1，-1最难过，0中性，1最开心)
            const maxDistance = 300;
            const rejectInfluence = Math.max(0, (maxDistance - distanceToReject) / maxDistance);
            const acceptInfluence = Math.max(0, (maxDistance - distanceToAccept) / maxDistance);
            
            const emotionLevel = acceptInfluence - rejectInfluence;
            
            // 更新眼睛
            if (emotionLevel > 0.3) {
                // 开心眯眼
                leftEye.style.height = '8px';
                rightEye.style.height = '8px';
                leftEye.style.borderRadius = '50px';
                rightEye.style.borderRadius = '50px';
            } else if (emotionLevel < -0.3) {
                // 难过瞪眼
                leftEye.style.height = '25px';
                rightEye.style.height = '25px';
                leftEye.style.borderRadius = '50%';
                rightEye.style.borderRadius = '50%';
            } else {
                // 中性
                leftEye.style.height = '20px';
                rightEye.style.height = '20px';
                leftEye.style.borderRadius = '50%';
                rightEye.style.borderRadius = '50%';
            }
            
            // 更新嘴巴
            if (emotionLevel > 0.5) {
                // 非常开心
                mouth.style.borderRadius = '0 0 80px 80px';
                mouth.style.transform = 'translateX(-50%) scaleY(1.2)';
                mouth.style.borderTopColor = 'transparent';
            } else if (emotionLevel > 0) {
                // 微笑
                mouth.style.borderRadius = '0 0 60px 60px';
                mouth.style.transform = 'translateX(-50%) scaleY(1)';
                mouth.style.borderTopColor = 'transparent';
            } else if (emotionLevel < -0.5) {
                // 非常难过
                mouth.style.borderRadius = '80px 80px 0 0';
                mouth.style.transform = 'translateX(-50%) scaleY(-1.2) translateY(20px)';
                mouth.style.borderTopColor = '#333';
                mouth.style.borderBottomColor = 'transparent';
            } else if (emotionLevel < 0) {
                // 难过
                mouth.style.borderRadius = '60px 60px 0 0';
                mouth.style.transform = 'translateX(-50%) scaleY(-1) translateY(10px)';
                mouth.style.borderTopColor = '#333';
                mouth.style.borderBottomColor = 'transparent';
            } else {
                // 中性/困惑
                mouth.style.borderRadius = '0';
                mouth.style.transform = 'translateX(-50%) scaleY(0.3)';
                mouth.style.borderTopColor = 'transparent';
                mouth.style.borderBottomColor = '#333';
            }
            
            // 更新脸部颜色
            const hue = 45 + emotionLevel * 30; // 从橙色到黄色
            face.style.background = `hsl(${hue}, 100%, 65%)`;
        }

        // 点击事件
        document.addEventListener('click', (e) => {
            if (e.target !== document.getElementById('menu') && 
                !document.getElementById('menu').contains(e.target) && 
                e.target !== document.querySelector('.menu-toggle')) {
                document.getElementById('menu').classList.remove('show');
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 设置初始表情为中性
            updateFaceExpression(window.innerWidth / 2, window.innerHeight / 2);
        });
    </script>
</body>
</html>
